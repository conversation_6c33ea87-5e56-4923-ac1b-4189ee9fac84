name: Verify Changeset

on:
  pull_request:
    branches:
      - main 
    paths:
      - 'apps/vscode-extension/**'
      - 'packages/srpc/**'
      - 'toolbar/**'

permissions:
  contents: read

jobs:
  verify:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Fetch all history for all tags and branches
          fetch-depth: 0 

      - name: Check for changeset file
        run: |
          #!/bin/bash
          echo "Workflow triggered due to changes in versioned paths."
          echo "Base ref: ${{ github.base_ref }}"
          echo "Head ref: ${{ github.head_ref }}"

          # Get the list of files changed between the base branch and current branch (PR head)
          # Using origin/ref syntax is generally more robust for PRs.
          CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref }}...${{ github.sha }})
          
          echo "Files changed in this PR:"
          echo "${CHANGED_FILES}"

          if [ -z "$CHANGED_FILES" ]; then
            echo "Warning: 'git diff' reported no changed files. This is unexpected as the workflow was triggered by path changes."
          fi

          # Check if any new changeset files (not README.md) have been created
          # Looking for .changeset/ANYTHING.md but not .changeset/README.md
          # The grep pattern matches files directly in .changeset/ ending with .md,
          # and explicitly excludes README.md using grep -v.
          NEW_CHANGESETS=$(echo "$CHANGED_FILES" | grep "^\\.changeset/[^/]*\\.md$" | grep -v "^\\.changeset/README\\.md$" || true)
          
          if [ -z "$NEW_CHANGESETS" ]; then
            echo "::error::Changes were made in versioned paths, but no changeset file was found. Please run 'pnpm changeset add' to create one."
            exit 1
          else
            echo "Changeset file(s) found for changes in versioned paths:"
            echo "$NEW_CHANGESETS"
            echo "Changeset requirement satisfied."
          fi 