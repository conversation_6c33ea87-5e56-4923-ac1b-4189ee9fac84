{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node"], "baseUrl": ".", "paths": {"@stagewise/extension-toolbar-srpc-contract": ["../packages/extension-toolbar-srpc-contract/dist"], "@stagewise/srpc": ["../packages/srpc/dist"]}}, "include": ["src", "scripts"], "exclude": ["node_modules", ".vscode-test", "out"]}