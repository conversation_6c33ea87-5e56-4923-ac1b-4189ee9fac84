// @ts-nocheck -- skip type checking
import * as docs_6 from "../content/docs/roadmap.mdx?collection=docs&hash=1748120553297"
import * as docs_5 from "../content/docs/quickstart.mdx?collection=docs&hash=1748120553297"
import * as docs_4 from "../content/docs/index.mdx?collection=docs&hash=1748120553297"
import * as docs_3 from "../content/docs/contributing.mdx?collection=docs&hash=1748120553297"
import * as docs_2 from "../content/docs/contact.mdx?collection=docs&hash=1748120553297"
import * as docs_1 from "../content/docs/community.mdx?collection=docs&hash=1748120553297"
import * as docs_0 from "../content/docs/agent-support.mdx?collection=docs&hash=1748120553297"
import * as blogPosts_0 from "../content/blog/test.mdx?collection=blogPosts&hash=1748120553297"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const blogPosts = _runtime.doc<typeof _source.blogPosts>([{ info: {"path":"test.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/blog/test.mdx"}, data: blogPosts_0 }]);
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"agent-support.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/agent-support.mdx"}, data: docs_0 }, { info: {"path":"community.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/community.mdx"}, data: docs_1 }, { info: {"path":"contact.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/contact.mdx"}, data: docs_2 }, { info: {"path":"contributing.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/contributing.mdx"}, data: docs_3 }, { info: {"path":"index.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/index.mdx"}, data: docs_4 }, { info: {"path":"quickstart.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/quickstart.mdx"}, data: docs_5 }, { info: {"path":"roadmap.mdx","absolutePath":"C:/Users/<USER>/stagewise/apps/website/content/docs/roadmap.mdx"}, data: docs_6 }], [])