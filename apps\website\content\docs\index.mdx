---
title: Introduction
---

# Eyesight for your AI-powered Code Editor

Welcome to **stagewise** — a browser toolbar that connects your frontend UI to your code AI agents in your code editor.

- 🧠 Select any element(s) in your web app
- 💬 Leave a comment on it
- 💡 Let your AI-Agent do the magic

> Perfect for devs tired of pasting folder paths into prompts. stagewise gives your AI real-time, browser-powered context.

## Features

- ⚡ Works out of the box
- 🛠️ Customise using your own configuration file
- 🔌 Connect to your own MCP server
- 📦 Does not impact bundle size
- 🧠 Sends DOM elements, screenshots & metadata to your AI agent
- 👇 Comment directly on live elements in the browser
- 🧪 Comes with playgrounds for React, Vue, and Svelte

## Get Started

- [Quickstart](./docs/quickstart)
- [Agent Support](./docs/agent-support)
- [Roadmap](./docs/roadmap)
- [Contributing](./docs/contributing)
- [Community & Support](./docs/community)
- [Contact](./docs/contact)
