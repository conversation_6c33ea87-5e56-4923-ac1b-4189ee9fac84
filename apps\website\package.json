{"name": "website", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo --port 3000", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@headlessui/react": "2.2.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-navigation-menu": "^1.2.12", "@stagewise/ui": "workspace:*", "fumadocs-core": "15.2.11", "fumadocs-mdx": "11.6.1", "fumadocs-ui": "15.2.11", "lucide-react": "^0.503.0", "next": "15.3.1", "posthog-js": "^1.242.3", "posthog-node": "^4.17.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@stagewise/toolbar-next": "workspace:*", "@stagewise/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@types/mdx": "^2.0.13", "@types/node": "22.15.2", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "zod": "^3.24.4"}}