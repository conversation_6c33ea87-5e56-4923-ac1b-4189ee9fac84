import type { <PERSON>ada<PERSON> } from 'next';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { A11yPlugin } from '@stagewise/plugin-a11y';
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StagewiseToolbar config={{ plugins: [A11yPlugin] }} />
        {children}
      </body>
    </html>
  );
}
