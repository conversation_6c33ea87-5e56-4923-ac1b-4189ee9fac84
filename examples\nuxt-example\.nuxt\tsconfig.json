// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../../../node_modules/.pnpm/nitropack@2.11.11_@azure+identity@4.9.1/node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../../../node_modules/.pnpm/nitropack@2.11.11_@azure+identity@4.9.1/node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../../../node_modules/.pnpm/nitropack@2.11.11_@azure+identity@4.9.1/node_modules/nitropack"
      ],
      "defu": [
        "../../../node_modules/.pnpm/defu@6.1.4/node_modules/defu"
      ],
      "h3": [
        "../../../node_modules/.pnpm/h3@1.15.3/node_modules/h3"
      ],
      "consola": [
        "../../../node_modules/.pnpm/consola@3.4.2/node_modules/consola"
      ],
      "ofetch": [
        "../../../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../../../node_modules/.pnpm/@unhead+vue@2.0.8_vue@3.5.13_typescript@5.8.3_/node_modules/@unhead/vue"
      ],
      "@nuxt/devtools": [
        "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../../../node_modules/.pnpm/@vue+runtime-core@3.5.13/node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../../../node_modules/.pnpm/@vue+compiler-sfc@3.5.13/node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../../../node_modules/.pnpm/unplugin-vue-router@0.12.0__6dea4a921e1a658f3dea90bedd184164/node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../../../node_modules/.pnpm/@nuxt+schema@3.17.2/node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt"
      ],
      "vite/client": [
        "../../../node_modules/.pnpm/vite@6.3.5_@types+node@22.1_7e8944db50601b4bd4f0e411bb2160f6/node_modules/vite/client"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../assets"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#vue-router": [
        "../../../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.13_typescript@5.8.3_/node_modules/vue-router"
      ],
      "#unhead/composables": [
        "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/head/runtime/composables/v3"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/3b120457-8f07-47c9-941e-891bc2e96a78"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "../**/*",
    "../.config/nuxt.*",
    "./nuxt.d.ts",
    "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools/runtime",
    "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools/dist/runtime",
    "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime",
    "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime",
    "..",
    "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/app",
    "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/app/compat/vue-demi",
    "../../../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.13_typescript@5.8.3_/node_modules/vue-router/dist/vue-router.node",
    "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/dist/head/runtime/composables/v3"
  ],
  "exclude": [
    "../dist",
    "../.data",
    "../node_modules",
    "../../../node_modules",
    "../../../node_modules/.pnpm/nuxt@3.17.2_@azure+identity_16e540b0fc430e3b8e6090e0a6ad5437/node_modules/nuxt/node_modules",
    "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools/node_modules",
    "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules",
    "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools/runtime/server",
    "../../../node_modules/.pnpm/@nuxt+devtools@2.4.0_vite@6_02837be4396c7018b44556a000bbcebd/node_modules/@nuxt/devtools/dist/runtime/server",
    "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server",
    "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}