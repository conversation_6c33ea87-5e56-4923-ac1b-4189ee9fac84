{"name": "react-example", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo dist node_modules", "dev": "vite --port 3003", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@stagewise/toolbar-react": "workspace:*", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5"}}