import { useState } from 'react';
import reactLogo from './assets/react.svg';
import viteLogo from '/vite.svg';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Stagewise</h1>
      <div className="card">
        <button type="button" onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
        <div style={{
          background: '#e3f2fd',
          padding: '16px',
          borderRadius: '8px',
          margin: '16px 0',
          border: '2px solid #2196f3'
        }}>
          <h3 style={{ margin: '0 0 8px 0', color: '#1976d2' }}>🎯 Stagewise Toolbar Status</h3>
          <p style={{ margin: '0', fontSize: '14px' }}>
            Look for the Stagewise toolbar in the corners of your screen.
            Try pressing <kbd>Ctrl+Shift+S</kbd> or right-clicking on elements.
            Check the browser console for debug information.
          </p>
        </div>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  );
}

export default App;
