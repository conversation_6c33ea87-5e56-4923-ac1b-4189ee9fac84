import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import { StagewiseToolbar } from '@stagewise/toolbar-react';

const toolbarConfig = {
  plugins: [],
};

// Add debug logging
console.log('Initializing Stagewise Toolbar...');

// Render the main app
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <StagewiseToolbar config={toolbarConfig} />
    <App />
  </StrictMode>,
);

// Add a debug message after a short delay
setTimeout(() => {
  const toolbar = document.querySelector('stagewise-companion-anchor');
  console.log('Stagewise toolbar element:', toolbar);
  if (toolbar) {
    console.log('Toolbar styles:', window.getComputedStyle(toolbar));
  }
}, 1000);
