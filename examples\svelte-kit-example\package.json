{"name": "svelte-kit-example", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo .svelte-kit node_modules", "dev": "vite dev --port 3004", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@stagewise/toolbar": "workspace:*", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.20.8", "@sveltejs/vite-plugin-svelte": "^5.0.3", "svelte": "^5.28.2", "svelte-check": "^4.1.7", "typescript": "^5.8.3", "vite": "^6.3.5"}}