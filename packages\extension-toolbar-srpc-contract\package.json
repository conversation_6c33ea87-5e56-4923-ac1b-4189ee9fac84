{"name": "@stagewise/extension-toolbar-srpc-contract", "description": "SRPC contract implementation for VS Code extension and toolbar communication", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsup --watch", "lint": "eslint src --ext .ts", "format": "biome format --write \"**/*.{ts,tsx,md}\""}, "dependencies": {"@stagewise/srpc": "workspace:*", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "22.15.2", "rimraf": "^5.0.10", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "3.1.2"}, "files": ["dist", "src"], "license": "AGPL-3.0-only", "publishConfig": {"access": "public"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./toolbar-bridge": {"types": "./dist/toolbar-bridge.d.ts", "require": "./dist/toolbar-bridge.js", "import": "./dist/toolbar-bridge.mjs"}, "./extension-bridge": {"types": "./dist/extension-bridge.d.ts", "require": "./dist/extension-bridge.js", "import": "./dist/extension-bridge.mjs"}}}