diff --git a/dist/components/dialog/dialog.js b/dist/components/dialog/dialog.js
index 7afe8fd6ba8c6550ce6fc9ffdb1dd0373e8c96fb..49f95ae64441b5068cbc56cb8b132c351ac2a3c1 100644
--- a/dist/components/dialog/dialog.js
+++ b/dist/components/dialog/dialog.js
@@ -1 +1 @@
-"use client";import n,{Fragment as N,createContext as ae,createRef as ie,useContext as pe,useEffect as se,useMemo as E,useReducer as de,useRef as W}from"react";import{useEscape as ue}from'../../hooks/use-escape.js';import{useEvent as A}from'../../hooks/use-event.js';import{useId as M}from'../../hooks/use-id.js';import{useInertOthers as Te}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as fe}from'../../hooks/use-is-touch-device.js';import{useOnDisappear as ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ce}from'../../hooks/use-outside-click.js';import{useOwnerDocument as me}from'../../hooks/use-owner.js';import{MainTreeProvider as $,useMainTreeNode as De,useRootContainers as Pe}from'../../hooks/use-root-containers.js';import{useScrollLock as ye}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Ee}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Ae}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as _e,State as x,useOpenClosed as j}from'../../internal/open-closed.js';import{ForcePortalRoot as Y}from'../../internal/portal-force-root.js';import{match as Ce}from'../../utils/match.js';import{RenderFeatures as J,forwardRefWithAs as _,useRender as L}from'../../utils/render.js';import{Description as K,useDescriptions as Re}from'../description/description.js';import{FocusTrap as Fe,FocusTrapFeatures as C}from'../focus-trap/focus-trap.js';import{Portal as be,PortalGroup as ve,useNestedPortals as xe}from'../portal/portal.js';import{Transition as Le,TransitionChild as X}from'../transition/transition.js';var Oe=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Oe||{}),he=(t=>(t[t.SetTitleId=0]="SetTitleId",t))(he||{});let Se={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},k=ae(null);k.displayName="DialogContext";function O(e){let t=pe(k);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ie(e,t){return Ce(t.type,Se,e,t)}let V=_(function(t,o){let a=M(),{id:l=`headlessui-dialog-${a}`,open:i,onClose:p,initialFocus:d,role:s="dialog",autoFocus:f=!0,__demoMode:u=!1,unmount:P=!1,...h}=t,R=W(!1);s=function(){return s==="dialog"||s==="alertdialog"?s:(R.current||(R.current=!0,console.warn(`Invalid role [${s}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let c=j();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let T=W(null),S=G(T,o),F=me(T),g=i?0:1,[b,q]=de(Ie,{titleId:null,descriptionId:null,panelRef:ie()}),m=A(()=>p(!1)),w=A(r=>q({type:0,id:r})),D=Ee()?g===0:!1,[z,Q]=xe(),Z={get current(){var r;return(r=b.panelRef.current)!=null?r:T.current}},v=De(),{resolveContainers:I}=Pe({mainTreeNode:v,portals:z,defaultContainers:[Z]}),B=c!==null?(c&x.Closing)===x.Closing:!1;Te(u||B?!1:D,{allowed:A(()=>{var r,H;return[(H=(r=T.current)==null?void 0:r.closest("[data-headlessui-portal]"))!=null?H:null]}),disallowed:A(()=>{var r;return[(r=v==null?void 0:v.closest("body > *:not(#headlessui-portal-root)"))!=null?r:null]})}),ce(D,I,r=>{r.preventDefault(),m()}),ue(D,F==null?void 0:F.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),m()}),ye(u||B?!1:D,F,I),ge(D,T,m);let[ee,te]=Re(),oe=E(()=>[{dialogState:g,close:m,setTitleId:w,unmount:P},b],[g,b,m,w,P]),U=E(()=>({open:g===0}),[g]),ne={ref:S,id:l,role:s,tabIndex:-1,"aria-modal":u?void 0:g===0?!0:void 0,"aria-labelledby":b.titleId,"aria-describedby":ee,unmount:P},re=!fe(),y=C.None;D&&!u&&(y|=C.RestoreFocus,y|=C.TabLock,f&&(y|=C.AutoFocus),re&&(y|=C.InitialFocus));let le=L();return n.createElement(_e,null,n.createElement(Y,{force:!0},n.createElement(be,null,n.createElement(k.Provider,{value:oe},n.createElement(ve,{target:T},n.createElement(Y,{force:!1},n.createElement(te,{slot:U},n.createElement(Q,null,n.createElement(Fe,{initialFocus:d,initialFocusFallback:T,containers:I,features:y},n.createElement(Ae,{value:m},le({ourProps:ne,theirProps:h,slot:U,defaultTag:Me,features:Ge,visible:g===0,name:"Dialog"})))))))))))}),Me="div",Ge=J.RenderStrategy|J.Static;function ke(e,t){let{transition:o=!1,open:a,...l}=e,i=j(),p=e.hasOwnProperty("open")||i!==null,d=e.hasOwnProperty("onClose");if(!p&&!d)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!p)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!d)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!l.static?n.createElement($,null,n.createElement(Le,{show:a,transition:o,unmount:l.unmount},n.createElement(V,{ref:t,...l}))):n.createElement($,null,n.createElement(V,{ref:t,open:a,...l}))}let we="div";function Be(e,t){let o=M(),{id:a=`headlessui-dialog-panel-${o}`,transition:l=!1,...i}=e,[{dialogState:p,unmount:d},s]=O("Dialog.Panel"),f=G(t,s.panelRef),u=E(()=>({open:p===0}),[p]),P=A(S=>{S.stopPropagation()}),h={ref:f,id:a,onClick:P},R=l?X:N,c=l?{unmount:d}:{},T=L();return n.createElement(R,{...c},T({ourProps:h,theirProps:i,slot:u,defaultTag:we,name:"Dialog.Panel"}))}let Ue="div";function He(e,t){let{transition:o=!1,...a}=e,[{dialogState:l,unmount:i}]=O("Dialog.Backdrop"),p=E(()=>({open:l===0}),[l]),d={ref:t,"aria-hidden":!0},s=o?X:N,f=o?{unmount:i}:{},u=L();return n.createElement(s,{...f},u({ourProps:d,theirProps:a,slot:p,defaultTag:Ue,name:"Dialog.Backdrop"}))}let Ne="h2";function We(e,t){let o=M(),{id:a=`headlessui-dialog-title-${o}`,...l}=e,[{dialogState:i,setTitleId:p}]=O("Dialog.Title"),d=G(t);se(()=>(p(a),()=>p(null)),[a,p]);let s=E(()=>({open:i===0}),[i]),f={ref:d,id:a};return L()({ourProps:f,theirProps:l,slot:s,defaultTag:Ne,name:"Dialog.Title"})}let $e=_(ke),je=_(Be),Dt=_(He),Ye=_(We),Pt=K,yt=Object.assign($e,{Panel:je,Title:Ye,Description:K});export{yt as Dialog,Dt as DialogBackdrop,Pt as DialogDescription,je as DialogPanel,Ye as DialogTitle};
+"use client";import n,{Fragment as N,createContext as ae,createRef as ie,useContext as se,useEffect as pe,useMemo as _,useReducer as de,useRef as W}from"react";import{useEscape as ue}from'../../hooks/use-escape.js';import{useEvent as C}from'../../hooks/use-event.js';import{useId as w}from'../../hooks/use-id.js';import{useInertOthers as Te}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as fe}from'../../hooks/use-is-touch-device.js';import{useOnDisappear as ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ce}from'../../hooks/use-outside-click.js';import{useOwnerDocument as me}from'../../hooks/use-owner.js';import{MainTreeProvider as $,useMainTreeNode as De,useRootContainers as Pe}from'../../hooks/use-root-containers.js';import{useScrollLock as ye}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Ee}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Ae}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as _e,State as x,useOpenClosed as j}from'../../internal/open-closed.js';import{ForcePortalRoot as Y}from'../../internal/portal-force-root.js';import{match as Ce}from'../../utils/match.js';import{RenderFeatures as J,forwardRefWithAs as R,useRender as O}from'../../utils/render.js';import{Description as K,useDescriptions as Re}from'../description/description.js';import{FocusTrap as Fe,FocusTrapFeatures as F}from'../focus-trap/focus-trap.js';import{Portal as be,PortalGroup as ve,useNestedPortals as Le}from'../portal/portal.js';import{Transition as xe,TransitionChild as X}from'../transition/transition.js';var Oe=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Oe||{}),he=(t=>(t[t.SetTitleId=0]="SetTitleId",t))(he||{});let Se={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},k=ae(null);k.displayName="DialogContext";function h(e){let t=se(k);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,h),o}return t}function Ie(e,t){return Ce(t.type,Se,e,t)}let V=R(function(t,o){let a=w(),{id:l=`headlessui-dialog-${a}`,open:i,onClose:s,initialFocus:d,role:p="dialog",autoFocus:f=!0,__demoMode:u=!1,unmount:y=!1,...S}=t,b=W(!1);p=function(){return p==="dialog"||p==="alertdialog"?p:(b.current||(b.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=j();i===void 0&&m!==null&&(i=(m&x.Open)===x.Open);let T=W(null),I=G(T,o),v=me(T),g=i?0:1,[L,q]=de(Ie,{titleId:null,descriptionId:null,panelRef:ie()}),D=C(()=>s(!1)),B=C(r=>q({type:0,id:r})),P=Ee()?g===0:!1,[z,Q]=Le(),Z={get current(){var r;return(r=L.panelRef.current)!=null?r:T.current}},c=De(),{resolveContainers:M}=Pe({mainTreeNode:c,portals:z,defaultContainers:[Z]}),H=m!==null?(m&x.Closing)===x.Closing:!1;Te(u||H?!1:P,{allowed:C(()=>{var r,A;return[(A=(r=T.current)==null?void 0:r.closest("[data-headlessui-portal]"))!=null?A:null]}),disallowed:C(()=>{var r,A;return[(r=c==null?void 0:c.closest("body > *:not(stagewise-companion-anchor)"))!=null?r:null,(A=c==null?void 0:c.closest("stagewise-companion-anchor > *:not(#stagewise-toolbar-portal-root)"))!=null?A:null]})}),ce(P,M,r=>{r.preventDefault(),D()}),ue(P,v==null?void 0:v.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),D()}),ye(u||H?!1:P,v,M),ge(P,T,D);let[ee,te]=Re(),oe=_(()=>[{dialogState:g,close:D,setTitleId:B,unmount:y},L],[g,L,D,B,y]),U=_(()=>({open:g===0}),[g]),ne={ref:I,id:l,role:p,tabIndex:-1,"aria-modal":u?void 0:g===0?!0:void 0,"aria-labelledby":L.titleId,"aria-describedby":ee,unmount:y},re=!fe(),E=F.None;P&&!u&&(E|=F.RestoreFocus,E|=F.TabLock,f&&(E|=F.AutoFocus),re&&(E|=F.InitialFocus));let le=O();return n.createElement(_e,null,n.createElement(Y,{force:!0},n.createElement(be,null,n.createElement(k.Provider,{value:oe},n.createElement(ve,{target:T},n.createElement(Y,{force:!1},n.createElement(te,{slot:U},n.createElement(Q,null,n.createElement(Fe,{initialFocus:d,initialFocusFallback:T,containers:M,features:E},n.createElement(Ae,{value:D},le({ourProps:ne,theirProps:S,slot:U,defaultTag:Me,features:we,visible:g===0,name:"Dialog"})))))))))))}),Me="div",we=J.RenderStrategy|J.Static;function Ge(e,t){let{transition:o=!1,open:a,...l}=e,i=j(),s=e.hasOwnProperty("open")||i!==null,d=e.hasOwnProperty("onClose");if(!s&&!d)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!d)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!l.static?n.createElement($,null,n.createElement(xe,{show:a,transition:o,unmount:l.unmount},n.createElement(V,{ref:t,...l}))):n.createElement($,null,n.createElement(V,{ref:t,open:a,...l}))}let ke="div";function Be(e,t){let o=w(),{id:a=`headlessui-dialog-panel-${o}`,transition:l=!1,...i}=e,[{dialogState:s,unmount:d},p]=h("Dialog.Panel"),f=G(t,p.panelRef),u=_(()=>({open:s===0}),[s]),y=C(I=>{I.stopPropagation()}),S={ref:f,id:a,onClick:y},b=l?X:N,m=l?{unmount:d}:{},T=O();return n.createElement(b,{...m},T({ourProps:S,theirProps:i,slot:u,defaultTag:ke,name:"Dialog.Panel"}))}let He="div";function Ue(e,t){let{transition:o=!1,...a}=e,[{dialogState:l,unmount:i}]=h("Dialog.Backdrop"),s=_(()=>({open:l===0}),[l]),d={ref:t,"aria-hidden":!0},p=o?X:N,f=o?{unmount:i}:{},u=O();return n.createElement(p,{...f},u({ourProps:d,theirProps:a,slot:s,defaultTag:He,name:"Dialog.Backdrop"}))}let Ne="h2";function We(e,t){let o=w(),{id:a=`headlessui-dialog-title-${o}`,...l}=e,[{dialogState:i,setTitleId:s}]=h("Dialog.Title"),d=G(t);pe(()=>(s(a),()=>s(null)),[a,s]);let p=_(()=>({open:i===0}),[i]),f={ref:d,id:a};return O()({ourProps:f,theirProps:l,slot:p,defaultTag:Ne,name:"Dialog.Title"})}let $e=R(Ge),je=R(Be),Dt=R(Ue),Ye=R(We),Pt=K,yt=Object.assign($e,{Panel:je,Title:Ye,Description:K});export{yt as Dialog,Dt as DialogBackdrop,Pt as DialogDescription,je as DialogPanel,Ye as DialogTitle};
diff --git a/dist/components/portal/portal.js b/dist/components/portal/portal.js
index 8ef75d426d191e6e67f331d34e27629344ed97f6..39e9382dc12c532253f87717266f6076d6f2630c 100644
--- a/dist/components/portal/portal.js
+++ b/dist/components/portal/portal.js
@@ -1 +1 @@
-"use client";import T,{Fragment as E,createContext as A,useContext as d,useEffect as G,useMemo as x,useRef as L,useState as c}from"react";import{createPortal as O}from"react-dom";import{useEvent as _}from'../../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as F}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as U}from'../../hooks/use-owner.js';import{useServerHandoffComplete as N}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as S,useSyncRefs as m}from'../../hooks/use-sync-refs.js';import{usePortalRoot as W}from'../../internal/portal-force-root.js';import{env as v}from'../../utils/env.js';import{forwardRefWithAs as y,useRender as R}from'../../utils/render.js';function j(e){let l=W(),o=d(H),[r,u]=c(()=>{var i;if(!l&&o!==null)return(i=o.current)!=null?i:null;if(v.isServer)return null;let t=e==null?void 0:e.getElementById("headlessui-portal-root");if(t)return t;if(e===null)return null;let a=e.createElement("div");return a.setAttribute("id","headlessui-portal-root"),e.body.appendChild(a)});return G(()=>{r!==null&&(e!=null&&e.body.contains(r)||e==null||e.body.appendChild(r))},[r,e]),G(()=>{l||o!==null&&u(o.current)},[o,u,l]),r}let M=E,I=y(function(l,o){let{ownerDocument:r=null,...u}=l,t=L(null),a=m(S(s=>{t.current=s}),o),i=U(t),f=r!=null?r:i,p=j(f),[n]=c(()=>{var s;return v.isServer?null:(s=f==null?void 0:f.createElement("div"))!=null?s:null}),P=d(g),b=N();C(()=>{!p||!n||p.contains(n)||(n.setAttribute("data-headlessui-portal",""),p.appendChild(n))},[p,n]),C(()=>{if(n&&P)return P.register(n)},[P,n]),F(()=>{var s;!p||!n||(n instanceof Node&&p.contains(n)&&p.removeChild(n),p.childNodes.length<=0&&((s=p.parentElement)==null||s.removeChild(p)))});let h=R();return b?!p||!n?null:O(h({ourProps:{ref:a},theirProps:u,slot:{},defaultTag:M,name:"Portal"}),n):null});function J(e,l){let o=m(l),{enabled:r=!0,ownerDocument:u,...t}=e,a=R();return r?T.createElement(I,{...t,ownerDocument:u,ref:o}):a({ourProps:{ref:o},theirProps:t,slot:{},defaultTag:M,name:"Portal"})}let X=E,H=A(null);function k(e,l){let{target:o,...r}=e,t={ref:m(l)},a=R();return T.createElement(H.Provider,{value:o},a({ourProps:t,theirProps:r,defaultTag:X,name:"Popover.Group"}))}let g=A(null);function le(){let e=d(g),l=L([]),o=_(t=>(l.current.push(t),e&&e.register(t),()=>r(t))),r=_(t=>{let a=l.current.indexOf(t);a!==-1&&l.current.splice(a,1),e&&e.unregister(t)}),u=x(()=>({register:o,unregister:r,portals:l}),[o,r,l]);return[l,x(()=>function({children:a}){return T.createElement(g.Provider,{value:u},a)},[u])]}let B=y(J),D=y(k),oe=Object.assign(B,{Group:D});export{oe as Portal,D as PortalGroup,le as useNestedPortals};
+"use client";import P,{Fragment as E,createContext as c,useContext as T,useEffect as A,useMemo as G,useRef as x,useState as L}from"react";import{createPortal as O}from"react-dom";import{useEvent as _}from"../../hooks/use-event";import{useIsoMorphicEffect as C}from"../../hooks/use-iso-morphic-effect";import{useOnUnmount as F}from"../../hooks/use-on-unmount";import{useOwnerDocument as U}from"../../hooks/use-owner";import{useServerHandoffComplete as S}from"../../hooks/use-server-handoff-complete";import{optionalRef as N,useSyncRefs as d}from"../../hooks/use-sync-refs";import{usePortalRoot as W}from"../../internal/portal-force-root";import{env as v}from"../../utils/env";import{forwardRefWithAs as m,useRender as g}from"../../utils/render";function j(t){let o=W(),n=T(b),[r,u]=L(()=>{var s,a;if(!o&&n!==null)return(s=n.current)!=null?s:null;if(v.isServer)return null;let e=t==null?void 0:t.getElementById("stagewise-toolbar-portal-root");if(e)return e;if(t===null)return null;let l=t.createElement("div");return l.setAttribute("id","stagewise-toolbar-portal-root"),l.style.pointerEvents="auto",((a=t.querySelector("stagewise-companion-anchor"))!=null?a:t.body).appendChild(l)});return A(()=>{var l;if(r===null)return;const e=(l=t==null?void 0:t.querySelector("stagewise-companion-anchor"))!=null?l:t==null?void 0:t.body;e!=null&&e.contains(r)||e==null||e.appendChild(r)},[r,t]),A(()=>{o||n!==null&&u(n.current)},[n,u,o]),r}let M=E,I=m(function(o,n){let{ownerDocument:r=null,...u}=o,e=x(null),l=d(N(i=>{e.current=i}),n),R=U(e),s=r!=null?r:R,a=j(s),[p]=L(()=>{var i;return v.isServer?null:(i=s==null?void 0:s.createElement("div"))!=null?i:null}),f=T(y),H=S();C(()=>{!a||!p||a.contains(p)||(p.setAttribute("data-headlessui-portal",""),a.appendChild(p))},[a,p]),C(()=>{if(p&&f)return f.register(p)},[f,p]),F(()=>{var i;!a||!p||(p instanceof Node&&a.contains(p)&&a.removeChild(p),a.childNodes.length<=0&&((i=a.parentElement)==null||i.removeChild(a)))});let h=g();return H?!a||!p?null:O(h({ourProps:{ref:l},theirProps:u,slot:{},defaultTag:M,name:"Portal"}),p):null});function q(t,o){let n=d(o),{enabled:r=!0,ownerDocument:u,...e}=t,l=g();return r?P.createElement(I,{...e,ownerDocument:u,ref:n}):l({ourProps:{ref:n},theirProps:e,slot:{},defaultTag:M,name:"Portal"})}let J=E,b=c(null);function X(t,o){let{target:n,...r}=t,e={ref:d(o)},l=g();return P.createElement(b.Provider,{value:n},l({ourProps:e,theirProps:r,defaultTag:J,name:"Popover.Group"}))}let y=c(null);function le(){let t=T(y),o=x([]),n=_(e=>(o.current.push(e),t&&t.register(e),()=>r(e))),r=_(e=>{let l=o.current.indexOf(e);l!==-1&&o.current.splice(l,1),t&&t.unregister(e)}),u=G(()=>({register:n,unregister:r,portals:o}),[n,r,o]);return[o,G(()=>function({children:l}){return P.createElement(y.Provider,{value:u},l)},[u])]}let k=m(q),B=m(X),oe=Object.assign(k,{Group:B});export{oe as Portal,B as PortalGroup,le as useNestedPortals};
diff --git a/dist/hooks/use-root-containers.js b/dist/hooks/use-root-containers.js
index 78b363c0ad8baeb9bec4a42f377f0aa8feb6f448..2dc670023d51705d1bb844df0013148feb9ac8bd 100644
--- a/dist/hooks/use-root-containers.js
+++ b/dist/hooks/use-root-containers.js
@@ -1 +1 @@
-import f,{createContext as M,useContext as d,useState as H}from"react";import{Hidden as E,HiddenFeatures as T}from'../internal/hidden.js';import{getOwnerDocument as L}from'../utils/owner.js';import{useEvent as s}from'./use-event.js';import{useOwnerDocument as h}from'./use-owner.js';function R({defaultContainers:l=[],portals:n,mainTreeNode:o}={}){let r=h(o),u=s(()=>{var i,c;let t=[];for(let e of l)e!==null&&(e instanceof HTMLElement?t.push(e):"current"in e&&e.current instanceof HTMLElement&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="headlessui-portal-root"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(m=>e.contains(m))||t.push(e));return t});return{resolveContainers:u,contains:s(t=>u().some(i=>i.contains(t)))}}let a=M(null);function O({children:l,node:n}){let[o,r]=H(null),u=b(n!=null?n:o);return f.createElement(a.Provider,{value:u},l,u===null&&f.createElement(E,{features:T.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=L(t))==null?void 0:i.querySelectorAll("html > *, body > *"))!=null?c:[])if(e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e!=null&&e.contains(t)){r(e);break}}}}))}function b(l=null){var n;return(n=d(a))!=null?n:l}export{O as MainTreeProvider,b as useMainTreeNode,R as useRootContainers};
+import f,{createContext as M,useContext as d,useState as H}from"react";import{Hidden as E,HiddenFeatures as T}from'../internal/hidden.js';import{getOwnerDocument as L}from'../utils/owner.js';import{useEvent as s}from'./use-event.js';import{useOwnerDocument as b}from'./use-owner.js';function R({defaultContainers:l=[],portals:n,mainTreeNode:o}={}){let r=b(o),u=s(()=>{var i,c;let t=[];for(let e of l)e!==null&&(e instanceof HTMLElement?t.push(e):"current"in e&&e.current instanceof HTMLElement&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="stagewise-toolbar-portal-root"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(m=>e.contains(m))||t.push(e));return t});return{resolveContainers:u,contains:s(t=>u().some(i=>i.contains(t)))}}let a=M(null);function O({children:l,node:n}){let[o,r]=H(null),u=h(n!=null?n:o);return f.createElement(a.Provider,{value:u},l,u===null&&f.createElement(E,{features:T.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=L(t))==null?void 0:i.querySelectorAll("html > *, body > *"))!=null?c:[])if(e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e!=null&&e.contains(t)){r(e);break}}}}))}function h(l=null){var n;return(n=d(a))!=null?n:l}export{O as MainTreeProvider,h as useMainTreeNode,R as useRootContainers};
