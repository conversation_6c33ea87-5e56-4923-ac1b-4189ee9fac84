/* SPDX-License-Identifier: AGPL-3.0-only
 * Toolbar app CSS
 * Copyright (C) 2025 Go<PERSON><PERSON>, Scharpff & Toews GbR
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

@import "tailwindcss" important;

stagewise-companion-anchor {
  all: initial;
  @apply text-zinc-950;
  font-family: "Inter", "Noto Color Emoji", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "SF Compact", "SF Pro", "Helvetica Neue", sans-serif !important;
  font-weight: normal !important;
  letter-spacing: normal !important;
  line-height: normal !important;
  text-rendering: auto !important;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  interpolate-size: allow-keywords;
}

@layer base {
  stagewise-companion-anchor * {
    min-height: 0;
    min-width: 0;
    position: relative;
  }
}

@supports (font-variation-settings: normal) {
  stagewise-companion-anchor {
    font-family: "InterVariable", "Noto Color Emoji", -apple-system,
      BlinkMacSystemFont, "Segoe UI", Roboto, "SF Compact", "SF Pro",
      "Helvetica Neue", sans-serif !important;
    font-optical-sizing: auto !important;
  }
}

@theme default {
  --color-background: var(--color-white);
  --color-foreground: var(--color-zinc-950);
  --color-muted: var(--color-zinc-100);
  --color-muted-foreground: var(--color-zinc-700);
  --color-border: var(--color-zinc-500);
}

#headlessui-portal-root {
  @apply fixed h-screen w-screen z-50;
}

#headlessui-portal-root > * {
  @apply pointer-events-auto;
}
