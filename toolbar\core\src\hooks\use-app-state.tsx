// SPDX-License-Identifier: AGPL-3.0-only
// Toolbar app state hook
// Copyright (C) 2025 Go<PERSON><PERSON>, <PERSON>harpff & Toews GbR

// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU Affero General Public License as
// published by the Free Software Foundation, either version 3 of the
// License, or (at your option) any later version.

// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU Affero General Public License for more details.

// You should have received a copy of the GNU Affero General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

// This hook manages the state of the companion app. It provides information about all high level stuff that affects what components are rendered etc.

// This hook provides information to all components about whether certain parts of the companion layout should be rendered or not.
// Components can use this information to hide themselves or show additional information.

import { createRef, type RefObject } from 'preact';
import { create, type StateCreator } from 'zustand';
import Super<PERSON><PERSON><PERSON> from 'superjson';
import { persist, type PersistStorage } from 'zustand/middleware';

export interface AppState {
  requestMainAppBlock: () => number;
  requestMainAppUnblock: () => number;
  discardMainAppBlock: (handle: number) => void;
  discardMainAppUnblock: (handle: number) => void;

  isMainAppBlocked: boolean;

  toolbarBoxRef: RefObject<HTMLElement | null>; // used to place popovers in case the reference is not available
  setToolbarBoxRef: (ref: RefObject<HTMLElement | null>) => void;
  unsetToolbarBoxRef: () => void;

  minimized: boolean;
  minimize: () => void;
  expand: () => void;

  promotedOnStartup: boolean; // This will be false initially, but will be set to true for the next re-hydration fo the store. If allows components to read and create an additional promoting animation.
  promotionFinished: () => void;
}

export interface InternalAppState extends AppState {
  appBlockRequestList: number[];
  appUnblockRequestList: number[];
  lastBlockRequestNumber: number;
  lastUnblockRequestNumber: number;
}

const createAppStore: StateCreator<AppState> = (s) => {
  const set = s as (
    partial:
      | InternalAppState
      | Partial<InternalAppState>
      | ((
          state: InternalAppState,
        ) => InternalAppState | Partial<InternalAppState>),
    replace?: boolean | undefined,
  ) => void;
  return {
    appBlockRequestList: [],
    appUnblockRequestList: [],
    lastBlockRequestNumber: 0,
    lastUnblockRequestNumber: 0,

    isMainAppBlocked: false,

    requestMainAppBlock: () => {
      let newHandleValue = 0;
      set((state) => {
        newHandleValue = state.lastBlockRequestNumber + 1;
        return {
          appBlockRequestList: [...state.appBlockRequestList, newHandleValue],
          lastBlockRequestNumber: newHandleValue,
          isMainAppBlocked: state.appUnblockRequestList.length === 0, // Unblock requests override block requests
        };
      });
      return newHandleValue;
    },
    requestMainAppUnblock: () => {
      let newHandleValue = 0;
      set((state) => {
        newHandleValue = state.lastUnblockRequestNumber + 1;
        return {
          appUnblockRequestList: [
            ...state.appUnblockRequestList,
            newHandleValue,
          ],
          lastUnblockRequestNumber: newHandleValue,
          isMainAppBlocked: false,
        };
      });
      return newHandleValue;
    },

    discardMainAppBlock: (handle: number) => {
      set((state) => {
        const newBlockRequestList = state.appBlockRequestList.filter(
          (h) => h !== handle,
        );
        return {
          appBlockRequestList: newBlockRequestList,
          isMainAppBlocked:
            newBlockRequestList.length > 0 &&
            state.appUnblockRequestList.length === 0,
        };
      });
    },

    discardMainAppUnblock: (handle: number) => {
      set((state) => {
        const newUnblockRequestList = state.appUnblockRequestList.filter(
          (h) => h !== handle,
        );
        return {
          appUnblockRequestList: newUnblockRequestList,
          isMainAppBlocked:
            state.appBlockRequestList.length > 0 &&
            newUnblockRequestList.length === 0,
        };
      });
    },

    toolbarBoxRef: createRef(),
    setToolbarBoxRef: (ref) => set(() => ({ toolbarBoxRef: ref })),
    unsetToolbarBoxRef: () => set(() => ({ toolbarBoxRef: createRef() })),

    minimized: false,
    minimize: () => set(() => ({ minimized: true })),
    expand: () => set(() => ({ minimized: false })),

    promotedOnStartup: false,
    promotionFinished: () => set(() => ({ promotedOnStartup: true })),
  };
};

function createSuperJSONStorage<T>(storage: Storage): PersistStorage<T> {
  return {
    getItem: (name) => {
      const str = storage.getItem(name);
      if (!str) return null;
      return SuperJSON.parse(str);
    },
    setItem: (name, value) => {
      storage.setItem(name, SuperJSON.stringify(value));
    },
    removeItem: (name) => storage.removeItem(name),
  };
}

export const useAppState = create(
  persist(createAppStore, {
    name: 'stgws:companion',
    storage: createSuperJSONStorage(sessionStorage),
    partialize: (state) => {
      return {
        minimized: state.minimized,
      };
    },
  }),
);
