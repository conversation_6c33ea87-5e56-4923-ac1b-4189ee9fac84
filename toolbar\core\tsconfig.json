{"compilerOptions": {"types": ["node"], "target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "preact", "paths": {"@/*": ["./src/*"], "react": ["./node_modules/@preact/compat/"], "react-dom": ["./node_modules/@preact/compat/"]}}, "basePath": ".", "include": ["src"]}