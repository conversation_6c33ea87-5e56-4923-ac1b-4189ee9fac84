{"name": "@stagewise/toolbar-react", "private": false, "version": "0.1.2", "type": "module", "main": "dist/index.umd.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "license": "AGPL-3.0-only", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf .turbo dist node_modules", "dev": "tsc -b && vite build --mode development", "build": "tsc -b && vite build", "build:toolbar": "tsc -b && vite build"}, "dependencies": {"@stagewise/toolbar": "workspace:*"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.3"}, "peerDependencies": {"@types/react": ">=18.0.0", "react": ">=18.0.0"}}