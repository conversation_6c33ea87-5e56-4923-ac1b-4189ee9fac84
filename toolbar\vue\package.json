{"name": "@stagewise/toolbar-vue", "private": false, "version": "0.1.2", "type": "module", "main": "dist/index.umd.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "license": "AGPL-3.0-only", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf .turbo dist node_modules", "dev": "tsc -b && vite build --mode development", "build": "tsc -b && vite build", "build:toolbar": "tsc -b && vite build"}, "dependencies": {"@stagewise/toolbar": "workspace:*"}, "devDependencies": {"@eslint/js": "^9.26.0", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.26.0", "globals": "^16.1.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.3", "vue": "^3.5.13"}, "peerDependencies": {"vue": ">=3.0.0"}}