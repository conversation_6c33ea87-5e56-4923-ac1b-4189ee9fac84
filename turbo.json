{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "out/**", "dist/**"]}, "//#check": {"cache": false}, "//#check:fix": {"cache": false}, "clean": {"cache": false}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}, "typecheck": {"dependsOn": ["^typecheck"]}, "test": {"cache": false, "persistent": true}}}